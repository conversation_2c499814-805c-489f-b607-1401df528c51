import numpy as np
import pandas as pd
import tqdm
from pyevtk.hl import unstructuredGridToVTK
import dask.dataframe as dd

# 读取文件并分割区块
with open("steady_results.csv", "r") as f:
    content = f.read().split("[Name]")

regions = []
for block in tqdm.tqdm(content[1:]):
    parts = block.split("[Data]")
    name = parts[0].strip()
    print(name)
    data_lines = parts[1].split("[Faces]")[0].strip().split('\n')
    faces_lines = parts[1].split("[Faces]")[1].strip().split('\n')

    # 解析数据表
    header = [h.strip() for h in data_lines[0].split(',')]
    # data = np.genfromtxt(data_lines[1:], delimiter=',')
    data = np.array([list(map(float, line.split(','))) for line in data_lines[1:]])
    # ddf  = dd.read_csv(data_lines, dtype="float32")
    # df = ddf.compute()
    df = pd.DataFrame(data, columns=header)

    # 解析面索引（兼容三角形和四边形）
    faces = []
    for line in faces_lines:  # 每个物体面片索引都是用的相对索引，如0，1，2
        if line.strip():  # 跳过空行
            indices = list(map(int, line.strip().split(',')))
            faces.append(indices)

    regions.append({"name": name, "data": df, "faces": faces})

# 合并所有区域的点和面
all_points = pd.concat([r["data"] for r in regions])
x = np.ascontiguousarray(all_points["X [ m ]"].to_numpy())  # 强制连续
y = np.ascontiguousarray(all_points["Y [ m ]"].to_numpy())
z = np.ascontiguousarray(all_points["Z [ m ]"].to_numpy())
all_faces = [face for r in regions for face in r["faces"]]

# 构建VTK所需的连接性、偏移量和单元类型
connectivity = []
offsets = []
cell_types = []
current_offset = 0

for face in all_faces:
    n_points = len(face)
    connectivity.extend(face)
    if n_points == 3:
        cell_types.append(5)  # VTK_TRIANGLE
    elif n_points == 4:
        cell_types.append(9)  # VTK_QUAD
    else:
        raise ValueError(f"Unsupported face with {n_points} points")
    current_offset += n_points
    offsets.append(current_offset)

# 导出为非结构化网格
unstructuredGridToVTK(
    "mixed_mesh",
    x, y, z,  # 使用连续的坐标数组
    connectivity=np.ascontiguousarray(connectivity),
    offsets=np.ascontiguousarray(offsets),
    cell_types=np.ascontiguousarray(cell_types),
    pointData={
        "velocity_u": np.ascontiguousarray(all_points["Velocity u [ m s^-1 ]"].to_numpy()),
        "velocity_v": np.ascontiguousarray(all_points["Velocity v [ m s^-1 ]"].to_numpy()),
        "velocity_w": np.ascontiguousarray(all_points["Velocity w [ m s^-1 ]"].to_numpy()),
        "pressure": np.ascontiguousarray(all_points["Pressure [ Pa ]"].to_numpy())
    }
)