{"id": "Downloads.sty_streamline", "path": "I:\\Downloads\\sty_streamline", "mtime": 1754443033, "size": 4096, "hash": "", "data_mtime": 1754298423, "dependencies": ["builtins", "abc", "typing"], "suppressed": [], "options": {"allow_untyped_globals": false, "disable_memoryview_promotion": false, "disallow_incomplete_defs": false, "disallow_any_generics": false, "strict_equality": false, "ignore_missing_imports": false, "disabled_error_codes": [], "old_type_inference": false, "enabled_error_codes": [], "disallow_any_decorated": false, "always_false": [], "warn_unused_ignores": false, "disable_bytearray_promotion": false, "disable_error_code": [], "ignore_errors": false, "disallow_untyped_defs": false, "bazel": false, "extra_checks": false, "warn_unreachable": false, "implicit_optional": false, "allow_redefinition": false, "follow_imports_for_stubs": false, "always_true": [], "local_partial_types": false, "strict_optional": true, "platform": "win32", "disallow_subclassing_any": false, "disallow_any_expr": false, "plugins": [], "enable_error_code": [], "disallow_any_explicit": false, "disallow_untyped_decorators": false, "disallow_any_unimported": false, "check_untyped_defs": false, "follow_imports": "silent", "warn_no_return": true, "mypyc": false, "implicit_reexport": true, "strict_concatenate": false, "disallow_untyped_calls": false, "warn_return_any": false}, "dep_prios": [5, 30, 30], "dep_lines": [1, 1, 1], "interface_hash": "650a5a6319a6600fb73f0cb97eb9e04ca80257a6c3b0450d2bb91038ca5ba557", "version_id": "1.10.1", "ignore_all": true, "plugin_data": null}