import numpy as np
import pandas as pd
from tqdm import tqdm
from pyevtk.hl import unstructuredGridToVTK
import vtk
from vtk.util import numpy_support
import dask.dataframe as dd
from dask.diagnostics import ProgressBar
import multiprocessing as mp
from typing import List, Dict, Any
import os

import numpy as np
import pandas as pd
from tqdm import tqdm
import vtk
from vtk.util import numpy_support
import os
import re


def load_and_process_data(filename: str) -> List[Dict[str, Any]]:
    """
    更稳健的文件加载方法，完全按照原始格式解析
    """
    with open(filename, 'r') as f:
        content = f.read()

    # 使用正则表达式精确分割区块
    blocks = re.split(r'\[Name\]\s*', content)[1:]

    regions = []
    for block in tqdm(blocks, desc="处理区域数据", unit="region"):
        # 使用更安全的方式分割数据段
        data_parts = re.split(r'\[Data\]\s*', block, maxsplit=1)
        if len(data_parts) < 2:
            continue

        name = data_parts[0].strip()
        print(name)
        remaining = data_parts[1]

        # 分割面和数据
        face_parts = re.split(r'\[Faces\]\s*', remaining, maxsplit=1)
        if len(face_parts) < 2:
            continue

        data_section = face_parts[0]
        faces_section = face_parts[1]

        # 处理数据部分
        data_lines = [line.strip() for line in data_section.split('\n') if line.strip()]
        header = [h.strip() for h in data_lines[0].split(',')]

        data = np.array([list(map(float, line.split(','))) for line in data_lines[1:]])
        df = pd.DataFrame(data, columns=header)

        # 处理面数据
        faces = []
        for line in faces_section.split('\n'):
            line = line.strip()
            if line:
                try:
                    face = list(map(int, line.split(',')))
                    faces.append(face)
                except:
                    continue

        regions.append({
            "name": name,
            "data": df,
            "faces": faces,
            "points": df[['X [ m ]', 'Y [ m ]', 'Z [ m ]']].values.astype(np.float32)
        })
    print("数据加载处理完成，共处理了", len(regions), "个区域")
    return regions


def create_vtk_pipeline(regions: List[Dict[str, Any]], output_dir: str = "visualization"):
    """
    创建VTK可视化管线
    生成流速迹线和压强云图
    """
    print("老板，可视化管线构建中...")

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 为每个区域创建可视化
    for region in tqdm(regions, desc="生成可视化", unit="region"):
        # 准备数据
        name = region["name"]
        points = region["points"]
        faces = region["faces"]
        df = region["data"]

        # 创建VTK点集
        vtk_points = vtk.vtkPoints()
        vtk_points.SetData(numpy_support.numpy_to_vtk(points))

        # 创建单元网格
        cells = vtk.vtkCellArray()

        for face in faces:
            if len(face) == 3:  # 三角形
                triangle = vtk.vtkTriangle()
                triangle.GetPointIds().SetId(0, face[0])
                triangle.GetPointIds().SetId(1, face[1])
                triangle.GetPointIds().SetId(2, face[2])
                cells.InsertNextCell(triangle)
            elif len(face) == 4:  # 四边形
                quad = vtk.vtkQuad()
                for i in range(4):
                    quad.GetPointIds().SetId(i, face[i])
                cells.InsertNextCell(quad)

        # 创建非结构化网格
        grid = vtk.vtkUnstructuredGrid()
        grid.SetPoints(vtk_points)
        grid.SetCells(vtk.VTK_TRIANGLE if len(faces[0]) == 3 else vtk.VTK_QUAD, cells)

        # 添加标量数据（压强）
        pressure = df['Pressure [ Pa ]'].values.astype(np.float32)
        pressure_array = numpy_support.numpy_to_vtk(pressure)
        pressure_array.SetName("Pressure")
        grid.GetPointData().AddArray(pressure_array)

        # 添加矢量数据（速度）
        velocity = df[['Velocity u [ m s^-1 ]', 'Velocity v [ m s^-1 ]', 'Velocity w [ m s^-1 ]']].values.astype(np.float32)
        velocity_array = numpy_support.numpy_to_vtk(velocity)
        velocity_array.SetName("Velocity")
        grid.GetPointData().AddArray(velocity_array)
        grid.GetPointData().SetActiveVectors("Velocity")

        # ================= 压强云图 =================
        # 创建mapper和actor
        pressure_mapper = vtk.vtkDataSetMapper()
        pressure_mapper.SetInputData(grid)
        pressure_mapper.SetScalarModeToUsePointData()
        pressure_mapper.SelectColorArray("Pressure")
        pressure_mapper.SetScalarRange(pressure.min(), pressure.max())

        pressure_actor = vtk.vtkActor()
        pressure_actor.SetMapper(pressure_mapper)

        # ================= 流速迹线 =================
        # 创建流线生成器
        seeds = vtk.vtkPointSource()
        seeds.SetNumberOfPoints(500)
        seeds.SetRadius(0.1)
        seeds.SetCenter(points.mean(axis=0))

        streamer = vtk.vtkStreamTracer()
        streamer.SetInputData(grid)
        streamer.SetSourceConnection(seeds.GetOutputPort())
        streamer.SetIntegrationDirectionToBoth()
        streamer.SetMaximumPropagation(1000)
        streamer.SetInitialIntegrationStep(0.1)
        streamer.SetIntegrationStepUnit(vtk.vtkStreamTracer.CELL_LENGTH_UNIT)

        stream_mapper = vtk.vtkPolyDataMapper()
        stream_mapper.SetInputConnection(streamer.GetOutputPort())
        stream_mapper.ScalarVisibilityOff()

        stream_actor = vtk.vtkActor()
        stream_actor.SetMapper(stream_mapper)
        stream_actor.GetProperty().SetColor(1, 0, 0)  # 红色流线

        # ================= 渲染场景 =================
        renderer = vtk.vtkRenderer()
        render_window = vtk.vtkRenderWindow()
        render_window.AddRenderer(renderer)
        render_window_interactor = vtk.vtkRenderWindowInteractor()
        render_window_interactor.SetRenderWindow(render_window)

        # 添加两种可视化
        renderer.AddActor(pressure_actor)
        renderer.AddActor(stream_actor)
        renderer.SetBackground(0.2, 0.3, 0.4)

        # 保存为图片和VTK文件
        save_visualization(render_window, grid, name, output_dir)

    print("可视化完成，老板！结果已保存在", os.path.abspath(output_dir))


def save_visualization(render_window, grid, name, output_dir):
    """保存可视化结果"""
    # 保存为PNG图片
    # window_to_image = vtk.vtkWindowToImageFilter()
    # window_to_image.SetInput(render_window)
    # window_to_image.SetScale(1)
    # window_to_image.SetInputBufferTypeToRGB()
    # window_to_image.ReadFrontBufferOff()
    # window_to_image.Update()

    # writer = vtk.vtkPNGWriter()
    # output_file = os.path.join(output_dir, f"{name}_visualization.png")
    # writer.SetFileName(output_file)
    # writer.SetInputConnection(window_to_image.GetOutputPort())
    # writer.Write()

    # 保存为VTK文件供后续分析
    vtk_file = os.path.join(output_dir, f"{name}_data.vtk")
    writer = vtk.vtkUnstructuredGridWriter()
    writer.SetFileName(vtk_file)
    writer.SetInputData(grid)
    writer.Write()


def main():
    """主执行函数"""

    # 加载和处理数据
    regions = load_and_process_data("steady_results.csv")

    # 生成可视化
    create_vtk_pipeline(regions)


if __name__ == "__main__":
    main()