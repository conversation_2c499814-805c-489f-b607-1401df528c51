import pickle

import numpy as np
import pandas as pd
from tqdm import tqdm
from pyevtk.hl import unstructuredGridToVTK
import vtk
from vtk.util import numpy_support
import dask.dataframe as dd
from dask.diagnostics import ProgressBar
import multiprocessing as mp
from typing import List, Dict, Any
import os

import numpy as np
import pandas as pd
from tqdm import tqdm
import vtk
from vtk.util import numpy_support
import os
import re


def load_and_process_data(filename: str) -> List[Dict[str, Any]]:
    """
    更稳健的文件加载方法，完全按照原始格式解析
    """
    with open(filename, 'r') as f:
        content = f.read()

    # 使用正则表达式精确分割区块
    blocks = re.split(r'\[Name\]\s*', content)[1:]

    regions = []
    offset = 0
    for block in tqdm(blocks, desc="处理区域数据", unit="region"):
        # 使用更安全的方式分割数据段
        data_parts = re.split(r'\[Data\]\s*', block, maxsplit=1)
        if len(data_parts) < 2:
            continue

        name = data_parts[0].strip()
        remaining = data_parts[1]

        # 分割面和数据
        face_parts = re.split(r'\[Faces\]\s*', remaining, maxsplit=1)
        if len(face_parts) < 2:
            continue

        data_section = face_parts[0]
        faces_section = face_parts[1]

        # 处理数据部分
        data_lines = [line.strip() for line in data_section.split('\n') if line.strip()]
        header = [h.strip() for h in data_lines[0].split(',')]

        data = np.array([list(map(float, line.split(','))) for line in data_lines[1:]])
        df = pd.DataFrame(data, columns=header)

        # 处理面数据
        faces = []
        ori_faces = []
        for line in faces_section.split('\n'):
            line = line.strip()
            if line:
                try:
                    ori_face = list(map(int, line.split(',')))
                    face = [(it + offset) for it in ori_face]
                    ori_faces.append(ori_face)
                    faces.append(face)
                except:
                    continue
        offset += len(data)
        regions.append({
            "name": name,
            "data": df,
            "faces": faces,
            "ori_faces": ori_faces,
            "points": df[['X [ m ]', 'Y [ m ]', 'Z [ m ]']].values.astype(np.float32)
        })
    print("数据加载处理完成，共处理了", len(regions), "个区域")
    return regions


def create_vertex_sampled_streamlines(regions: List[Dict[str, Any]], output_dir: str = "vertex_streamlines"):
    """
    按顶点采样生成流速迹线（修复版）
    使用入口区域的所有顶点作为流线起始点，跳过结构化重采样
    """
    print("正在创建按顶点采样的流线可视化...")

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 合并所有区域数据
    all_points = []
    all_data = []
    all_faces = []
    region_map = {}

    for region in regions:
        region_map[region["name"]] = region
        all_points.append(region["points"])
        all_data.append(region["data"])
        all_faces.extend(region["faces"])

    # 检查必要区域
    required_regions = ["inlet1too", "inlet2too", "inlet3too", "inlet4too", "outlettoo"]
    for req in required_regions:
        if req not in region_map:
            raise ValueError(f"关键区域 {req} 缺失，无法完成流线可视化")

    # 创建全局网格
    global_points = np.concatenate(all_points)
    global_df = pd.concat(all_data)

    vtk_points = vtk.vtkPoints()
    vtk_points.SetData(numpy_support.numpy_to_vtk(global_points))

    grid = vtk.vtkUnstructuredGrid()
    grid.SetPoints(vtk_points)

    # 添加单元（三角形/四边形）
    for face in all_faces:
        if len(face) == 3:
            triangle = vtk.vtkTriangle()
            for i in range(3):
                triangle.GetPointIds().SetId(i, face[i])
            grid.InsertNextCell(triangle.GetCellType(), triangle.GetPointIds())
        elif len(face) == 4:
            quad = vtk.vtkQuad()
            for i in range(4):
                quad.GetPointIds().SetId(i, face[i])
            grid.InsertNextCell(quad.GetCellType(), quad.GetPointIds())

    # 添加速度场
    velocity = global_df[['Velocity u [ m s^-1 ]', 'Velocity v [ m s^-1 ]', 'Velocity w [ m s^-1 ]']].values.astype(np.float32)
    velocity_array = numpy_support.numpy_to_vtk(velocity)
    velocity_array.SetName("Velocity")
    velocity_array.SetNumberOfComponents(3)
    grid.GetPointData().SetVectors(velocity_array)
    grid.GetPointData().SetActiveVectors("Velocity")

    # ✅ 调试：打印速度场范围
    print("速度场范围（min ~ max）:")
    print("  u:", velocity[:, 0].min(), "~", velocity[:, 0].max())
    print("  v:", velocity[:, 1].min(), "~", velocity[:, 1].max())
    print("  w:", velocity[:, 2].min(), "~", velocity[:, 2].max())

    print(f"网格点数: {grid.GetNumberOfPoints()}")
    print(f"网格单元数: {grid.GetNumberOfCells()}")
    print(f"网格边界: {grid.GetBounds()}")

    # ================= 开始流线生成 =================
    renderer = vtk.vtkRenderer()
    colors = [(1, 0, 0), (0, 1, 0), (0, 0, 1), (1, 1, 0), (0, 1, 1)]
    inlet_names = ["inlet1too", "inlet2too", "inlet3too", "inlet4too",]

    locator = vtk.vtkPointLocator()
    locator.SetDataSet(grid)
    locator.BuildLocator()

    for i, inlet_name in enumerate(inlet_names):
        if inlet_name not in region_map:
            print(f"⚠️ 未找到入口 {inlet_name}")
            continue

        inlet_region = region_map[inlet_name]
        inlet_points = inlet_region["points"]

        seeds = vtk.vtkPolyData()
        seed_points = vtk.vtkPoints()

        # 添加所有入口顶点作为种子点
        valid_seeds = 0
        for pt in inlet_points:
            closest_id = locator.FindClosestPoint(pt)
            if closest_id != -1:
                seed_points.InsertNextPoint(pt[0], pt[1], pt[2])
                valid_seeds += 1

        seeds.SetPoints(seed_points)

        if valid_seeds == 0:
            print(f"⚠️ {inlet_name} 没有有效种子点，跳过")
            continue

        # 创建种子单元
        verts = vtk.vtkCellArray()
        for j in range(valid_seeds):
            verts.InsertNextCell(1)
            verts.InsertCellPoint(j)
        seeds.SetVerts(verts)

        print(f"  使用 {valid_seeds} 个有效顶点作为种子点")

        # 流线设置
        integ = vtk.vtkRungeKutta4()
        streamer = vtk.vtkStreamTracer()
        streamer.SetIntegrator(integ)
        streamer.SetInputData(grid)
        streamer.SetSourceData(seeds)

        streamer.SetMaximumPropagation(2000)  # ✅ 更长追踪
        streamer.SetInitialIntegrationStep(0.05)
        streamer.SetMinimumIntegrationStep(1e-5)
        streamer.SetMaximumIntegrationStep(0.1)
        streamer.SetTerminalSpeed(1e-6)  # ✅ 更低终止速度
        streamer.SetIntegrationDirectionToBoth()  # ✅ 双向追踪
        streamer.Update()

        streamlines = streamer.GetOutput()
        print(f"  生成的流线数: {streamlines.GetNumberOfCells()}")
        print(f"  流线点数: {streamlines.GetNumberOfPoints()}")

        mapper = vtk.vtkPolyDataMapper()
        mapper.SetInputConnection(streamer.GetOutputPort())
        mapper.ScalarVisibilityOff()

        actor = vtk.vtkActor()
        actor.SetMapper(mapper)
        actor.GetProperty().SetColor(colors[i])
        actor.GetProperty().SetLineWidth(2)
        actor.GetProperty().SetOpacity(0.8)
        renderer.AddActor(actor)

    outlet_region = region_map["outlettoo"]
    outlet_points = outlet_region["points"]
    print("出口区域坐标范围：")
    print("  X:", outlet_points[:, 0].min(), "~", outlet_points[:, 0].max())
    print("  Y:", outlet_points[:, 1].min(), "~", outlet_points[:, 1].max())
    print("  Z:", outlet_points[:, 2].min(), "~", outlet_points[:, 2].max())

    # ✅ 在出口区域生成规则网格种子点（反向追踪用）
    outlet_bbox_min = outlet_points.min(axis=0)
    outlet_bbox_max = outlet_points.max(axis=0)

    reverse_seed = vtk.vtkPolyData()
    reverse_points = vtk.vtkPoints()

    # 生成 5×5 网格种子点
    nx, ny = 5, 5
    x_vals = np.linspace(outlet_bbox_min[0], outlet_bbox_max[0], nx)
    y_vals = np.linspace(outlet_bbox_min[1], outlet_bbox_max[1], ny)
    z_val = outlet_points[:, 2].mean()

    for x in x_vals:
        for y in y_vals:
            reverse_points.InsertNextPoint(x, y, z_val)

    reverse_seed.SetPoints(reverse_points)
    reverse_verts = vtk.vtkCellArray()
    for i in range(reverse_points.GetNumberOfPoints()):
        reverse_verts.InsertNextCell(1)
        reverse_verts.InsertCellPoint(i)
    reverse_seed.SetVerts(reverse_verts)

    reverse_streamer = vtk.vtkStreamTracer()
    reverse_streamer.SetIntegrator(vtk.vtkRungeKutta4())
    reverse_streamer.SetInputData(grid)
    reverse_streamer.SetSourceData(reverse_seed)
    reverse_streamer.SetMaximumPropagation(2000)
    reverse_streamer.SetIntegrationDirectionToBackward()
    reverse_streamer.Update()

    print("反向流线数：", reverse_streamer.GetOutput().GetNumberOfCells())


    # 添加可视化辅助
    _add_inlet_outlet_visualization(renderer, region_map, inlet_names, colors)
    _setup_and_save_visualization(renderer, grid, output_dir, "vertex_sampled_streamlines", "按顶点采样的流线可视化")


def _add_inlet_outlet_visualization(renderer, region_map, inlet_names, colors):
    """添加入口和出口区域的可视化"""
    print("添加入口平面可视化...")
    for i, inlet_name in enumerate(inlet_names):
        if inlet_name not in region_map:
            continue

        inlet_region = region_map[inlet_name]

        # 创建入口表面
        inlet_poly = vtk.vtkPolyData()
        inlet_points_vtk = vtk.vtkPoints()

        # 添加入口点
        for point in inlet_region["points"]:
            inlet_points_vtk.InsertNextPoint(point[0], point[1], point[2])

        inlet_poly.SetPoints(inlet_points_vtk)

        # 添加入口面
        inlet_cells = vtk.vtkCellArray()
        for face in inlet_region["ori_faces"]:
            if len(face) == 3:
                triangle = vtk.vtkTriangle()
                triangle.GetPointIds().SetId(0, face[0])
                triangle.GetPointIds().SetId(1, face[1])
                triangle.GetPointIds().SetId(2, face[2])
                inlet_cells.InsertNextCell(triangle)
            elif len(face) == 4:
                quad = vtk.vtkQuad()
                for j in range(4):
                    quad.GetPointIds().SetId(j, face[j])
                inlet_cells.InsertNextCell(quad)

        inlet_poly.SetPolys(inlet_cells)

        # 创建mapper和actor
        inlet_mapper = vtk.vtkPolyDataMapper()
        inlet_mapper.SetInputData(inlet_poly)

        inlet_actor = vtk.vtkActor()
        inlet_actor.SetMapper(inlet_mapper)
        inlet_actor.GetProperty().SetColor(colors[i])
        inlet_actor.GetProperty().SetOpacity(0.5)  # 半透明

        renderer.AddActor(inlet_actor)

    print("入口平面可视化添加完成")

    # 添加出口区域可视化
    print("添加出口区域可视化...")
    outlet_region = region_map["outlettoo"]

    # 创建出口表面
    outlet_poly = vtk.vtkPolyData()
    outlet_points_vtk = vtk.vtkPoints()

    # 添加出口点
    for point in outlet_region["points"]:
        outlet_points_vtk.InsertNextPoint(point[0], point[1], point[2])

    outlet_poly.SetPoints(outlet_points_vtk)

    outlet_cells = vtk.vtkCellArray()
    for face in outlet_region["ori_faces"]:
        if len(face) == 3:
            triangle = vtk.vtkTriangle()
            triangle.GetPointIds().SetId(0, face[0])
            triangle.GetPointIds().SetId(1, face[1])
            triangle.GetPointIds().SetId(2, face[2])
            outlet_cells.InsertNextCell(triangle)
        elif len(face) == 4:
            quad = vtk.vtkQuad()
            for j in range(4):
                quad.GetPointIds().SetId(j, face[j])
            outlet_cells.InsertNextCell(quad)

    outlet_poly.SetPolys(outlet_cells)

    outlet_mapper = vtk.vtkPolyDataMapper()
    outlet_mapper.SetInputData(outlet_poly)

    outlet_actor = vtk.vtkActor()
    outlet_actor.SetMapper(outlet_mapper)
    outlet_actor.GetProperty().SetColor(0.5, 0.5, 0.5)  # 灰色
    outlet_actor.GetProperty().SetOpacity(0.3)  # 半透明

    renderer.AddActor(outlet_actor)
    print("出口区域可视化添加完成")


def _setup_and_save_visualization(renderer, grid, output_dir, filename_prefix, window_title):
    """设置渲染场景并保存结果"""
    # 设置渲染场景
    renderer.SetBackground(0.1, 0.2, 0.3)
    renderer.ResetCamera()

    render_window = vtk.vtkRenderWindow()
    render_window.AddRenderer(renderer)
    render_window.SetSize(1200, 900)
    render_window.SetWindowName(window_title)

    render_window_interactor = vtk.vtkRenderWindowInteractor()
    render_window_interactor.SetRenderWindow(render_window)

    # 保存为VTK文件
    vtk_file = os.path.join(output_dir, f"{filename_prefix}.vtk")
    writer = vtk.vtkUnstructuredGridWriter()
    writer.SetFileName(vtk_file)
    writer.SetInputData(grid)
    writer.Write()

    # 保存为PNG图片
    window_to_image = vtk.vtkWindowToImageFilter()
    window_to_image.SetInput(render_window)
    window_to_image.SetScale(1)
    window_to_image.SetInputBufferTypeToRGB()
    window_to_image.ReadFrontBufferOff()
    window_to_image.Update()

    writer = vtk.vtkPNGWriter()
    output_file = os.path.join(output_dir, f"{filename_prefix}.png")
    writer.SetFileName(output_file)
    writer.SetInputConnection(window_to_image.GetOutputPort())
    writer.Write()

    print(f"流线可视化完成！结果已保存在 {os.path.abspath(output_dir)}")
    print("红色: inlet1too, 绿色: inlet2too, 蓝色: inlet3too, 黄色: inlet4too")
    print("灰色半透明区域: outlettoo")

    # 交互式显示
    render_window.Render()
    render_window_interactor.Start()


def main():
    """主执行函数"""
    # 加载和处理数据
    cache_file = os.path.join(os.getcwd(), "cache.pkl")
    if os.path.exists(cache_file):
        with open(cache_file, "rb") as f:
            regions = pickle.load(f)
    else:
        regions = load_and_process_data("steady_results.csv")
        with open(cache_file, "wb") as f:
            pickle.dump(regions, f)

    create_vertex_sampled_streamlines(regions)


if __name__ == "__main__":
    main()