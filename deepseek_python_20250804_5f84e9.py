import sys

import vtk
from vtkmodules.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor
from PyQt5.QtWidgets import QMainWindow, QApplication
from PyQt5.QtCore import qDebug

class VTKStreamline(QMainWindow):
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 初始化UI
        self.initUI()
        
        # 设置VTK渲染
        self.setupVTK()
    
    def initUI(self):
        self._pVTKWidget = QVTKRenderWindowInteractor(self)
        self.setCentralWidget(self._pVTKWidget)
        # self.showMaximized()
    
    def setupVTK(self):
        # 1. 读取数据
        plot3dReader = vtk.vtkMultiBlockPLOT3DReader()
        plot3dReader.SetXYZFileName("combxyz.bin")
        plot3dReader.SetQFileName("combq.bin")
        plot3dReader.SetScalarFunctionNumber(100)
        plot3dReader.SetVectorFunctionNumber(202)
        print(str(plot3dReader.GetOutput().GetNumberOfBlocks()))  # 0
        
        # 更新管线
        plot3dReader.Update()
        print(str(plot3dReader.GetOutput().GetNumberOfBlocks()))  # 1
        
        plot3dOutput = plot3dReader.GetOutput().GetBlock(0)
        print(type(plot3dOutput))  # <class 'vtkmodules.util.data_model.StructuredGrid'>

        seeds = vtk.vtkLineSource()
        seeds.SetResolution(25)
        seeds.SetPoint1(15, -5, 32)
        seeds.SetPoint2(15, 5, 32)

        # 四阶龙格库塔求解微分
        integ = vtk.vtkRungeKutta4()
        
        # 通过整合矢量场生成流线
        streamer = vtk.vtkStreamTracer()
        streamer.SetIntegrator(integ)
        streamer.SetInputData(plot3dOutput)
        # streamer.SetStartPosition(15, 5, 32)
        streamer.SetMaximumPropagation(100)
        streamer.SetInitialIntegrationStep(0.1)  # 修正了拼写错误(原C++代码有拼写错误)
        streamer.SetIntegrationDirectionToBackward()  # 修正了拼写错误(原C++代码有拼写错误)
        streamer.SetSourceConnection(seeds.GetOutputPort())
        
        # 2. 过滤器
        # 产生结构化栅格边界的一个线轮廓
        outline = vtk.vtkStructuredGridOutlineFilter()
        outline.SetInputData(plot3dOutput)
        
        # 3. 映射器
        outlineMapper = vtk.vtkPolyDataMapper()
        singleMapper = vtk.vtkPolyDataMapper()
        singleMapper.SetScalarRange(plot3dOutput.GetPointData().GetScalars().GetRange())

        scalarSurface = vtk.vtkRuledSurfaceFilter()
        scalarSurface.SetInputConnection(streamer.GetOutputPort())
        # 设置生成方法
        scalarSurface.SetRuledModeToPointWalk()

        # 生成流带
        # scalarSurface.SetOnRatio(2)


        # 4. 演员
        outlineActor = vtk.vtkActor()
        singleActor = vtk.vtkActor()
        
        # 5. 渲染器
        renderer = vtk.vtkRenderer()
        renderer.SetBackground(0.3, 0.6, 0.3)  # 背景颜色: 绿色
        
        # 6. 连接管线
        outlineMapper.SetInputConnection(outline.GetOutputPort())

        # 生成流线
        # singleMapper.SetInputConnection(streamer.GetOutputPort())

        # 生成流面/流带
        singleMapper.SetInputConnection(scalarSurface.GetOutputPort())

        outlineActor.SetMapper(outlineMapper)
        singleActor.SetMapper(singleMapper)
        renderer.AddActor(outlineActor)
        renderer.AddActor(singleActor)
        
        self._pVTKWidget.GetRenderWindow().AddRenderer(renderer)
        self._pVTKWidget.GetRenderWindow().Render()

    def __del__(self):
        pass


if __name__ == '__main__':
    app = QApplication(sys.argv)
    app.setApplicationName("流线测试")
    app.setApplicationVersion("1.0")

    # 创建并显示主窗口
    window = VTKStreamline()
    window.show()

    # 运行应用
    sys.exit(app.exec_())
